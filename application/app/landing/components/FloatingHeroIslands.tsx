/** @format */

'use client';

import { colors } from '@/app/colors';
import { LANDING_PAGE_DATA } from '@/app/shared/poi/constants';
import React, { useState } from 'react';
import {
	FiCamera,
	FiCoffee,
	FiGlobe,
	FiHeart,
	FiMapPin,
	FiMessageCircle,
	FiStar,
	FiTrendingUp,
	FiZap,
} from 'react-icons/fi';

interface FloatingIslandProps {
	id: string;
	position: { x: number; y: number; z: number };
	children: React.ReactNode;
	hoverEffect: 'lift' | 'glow' | 'rotate';
	connectionLines?: string[];
	delay?: number;
}

interface FloatingHeroIslandsProps {
	onGetStarted: () => void;
}

const FloatingIsland: React.FC<FloatingIslandProps> = ({
	children,
	hoverEffect,
	delay = 0,
}) => {
	const [isHovered, setIsHovered] = useState(false);

	const getHoverStyles = () => {
		if (!isHovered) return {};

		switch (hoverEffect) {
			case 'lift':
				return {
					transform: `translateY(-8px) scale(1.02)`,
					boxShadow: `0 12px 24px rgba(51, 194, 255, 0.15)`,
				};
			case 'glow':
				return {
					transform: `scale(1.02)`,
					boxShadow: `0 8px 32px rgba(128, 237, 153, 0.2)`,
				};
			case 'rotate':
				return {
					transform: `scale(1.05) rotate(2deg)`,
					boxShadow: `0 20px 40px rgba(51, 194, 255, 0.2)`,
				};
			default:
				return {};
		}
	};

	return (
		<div
			className='w-full transition-all duration-500 ease-out'
			style={{
				animationDelay: `${delay}ms`,
				...getHoverStyles(),
			}}
			onMouseEnter={() => setIsHovered(true)}
			onMouseLeave={() => setIsHovered(false)}>
			{children}
		</div>
	);
};

const SearchPreviewCard: React.FC = () => (
	<div
		className='w-full max-w-sm p-4 sm:p-5 rounded-2xl border backdrop-blur-sm cursor-default shadow-lg hover:shadow-xl transition-shadow duration-300'
		style={{
			background: `linear-gradient(135deg, ${colors.ui.blue50} 0%, ${colors.neutral.cloudWhite} 100%)`,
			borderColor: colors.ui.gray200,
		}}>
		<div className='flex items-center space-x-3 mb-4'>
			<div
				className='w-8 h-8 sm:w-10 sm:h-10 rounded-xl flex items-center justify-center flex-shrink-0'
				style={{
					background: `linear-gradient(135deg, ${colors.brand.blue} 0%, ${colors.brand.green} 100%)`,
				}}>
				<FiMapPin className='w-4 h-4 sm:w-5 sm:h-5 text-white' />
			</div>
			<div className='min-w-0 flex-1'>
				<h3
					className='font-semibold text-sm sm:text-base truncate'
					style={{ color: colors.neutral.textBlack }}>
					{LANDING_PAGE_DATA.searchPreview.title}
				</h3>
				<p
					className='text-xs sm:text-sm truncate'
					style={{ color: colors.neutral.slateGray }}>
					{LANDING_PAGE_DATA.searchPreview.subtitle}
				</p>
			</div>
		</div>

		<div className='space-y-2 sm:space-y-3'>
			{LANDING_PAGE_DATA.searchPreview.locations.map((location, index) => (
				<div
					key={index}
					className='flex items-center justify-between p-2 sm:p-3 rounded-lg'
					style={{ background: colors.ui.blue50 }}>
					<div className='flex items-center space-x-2 sm:space-x-3 min-w-0 flex-1'>
						<div
							className='w-2 h-2 rounded-full flex-shrink-0'
							style={{ background: colors.brand.green }}></div>
						<div className='min-w-0 flex-1'>
							<p
								className='font-medium text-xs sm:text-sm truncate'
								style={{ color: colors.neutral.textBlack }}>
								{location.name}
							</p>
							<p
								className='text-xs truncate'
								style={{ color: colors.neutral.slateGray }}>
								{location.type}
							</p>
						</div>
					</div>
					<div className='flex items-center space-x-1 flex-shrink-0'>
						<FiStar
							className='w-3 h-3'
							style={{ color: colors.brand.green }}
						/>
						<span
							className='text-xs font-medium'
							style={{ color: colors.neutral.textBlack }}>
							{location.rating}
						</span>
					</div>
				</div>
			))}
		</div>
	</div>
);

const AIChatBubble: React.FC = () => (
	<div
		className='w-full max-w-sm p-4 sm:p-5 rounded-2xl border backdrop-blur-sm cursor-default shadow-lg hover:shadow-xl transition-shadow duration-300'
		style={{
			background: `linear-gradient(135deg, ${colors.ui.green50} 0%, ${colors.neutral.cloudWhite} 100%)`,
			borderColor: colors.ui.gray200,
		}}>
		<div className='flex items-center space-x-3 mb-4'>
			<div
				className='w-8 h-8 sm:w-10 sm:h-10 rounded-xl flex items-center justify-center flex-shrink-0'
				style={{
					background: `linear-gradient(135deg, ${colors.brand.green} 0%, ${colors.brand.blue} 100%)`,
				}}>
				<FiMessageCircle className='w-4 h-4 sm:w-5 sm:h-5 text-white' />
			</div>
			<div className='min-w-0 flex-1'>
				<h3
					className='font-semibold text-sm sm:text-base truncate'
					style={{ color: colors.neutral.textBlack }}>
					{LANDING_PAGE_DATA.aiConversation.title}
				</h3>
				<p
					className='text-xs sm:text-sm truncate'
					style={{ color: colors.neutral.slateGray }}>
					{LANDING_PAGE_DATA.aiConversation.subtitle}
				</p>
			</div>
		</div>

		<div className='space-y-3 sm:space-y-4'>
			<div className='flex justify-end'>
				<div
					className='max-w-[85%] p-2 sm:p-3 rounded-2xl rounded-tr-sm'
					style={{ background: colors.brand.blue, color: 'white' }}>
					<p className='text-xs sm:text-sm'>
						{LANDING_PAGE_DATA.aiConversation.userMessage}
					</p>
				</div>
			</div>

			<div className='flex justify-start'>
				<div
					className='max-w-[85%] p-2 sm:p-3 rounded-2xl rounded-tl-sm'
					style={{
						background: colors.ui.gray100,
						color: colors.neutral.textBlack,
					}}>
					<p className='text-xs sm:text-sm'>
						{LANDING_PAGE_DATA.aiConversation.aiResponse}
					</p>
					<div className='flex items-center space-x-2 mt-2'>
						<div className='flex space-x-1'>
							<div
								className='w-1 h-1 rounded-full animate-pulse'
								style={{ background: colors.brand.green }}></div>
							<div
								className='w-1 h-1 rounded-full animate-pulse'
								style={{
									background: colors.brand.green,
									animationDelay: '0.2s',
								}}></div>
							<div
								className='w-1 h-1 rounded-full animate-pulse'
								style={{
									background: colors.brand.green,
									animationDelay: '0.4s',
								}}></div>
						</div>
						<span
							className='text-xs'
							style={{ color: colors.neutral.slateGray }}>
							{LANDING_PAGE_DATA.aiConversation.typingIndicator}
						</span>
					</div>
				</div>
			</div>
		</div>
	</div>
);

const CentralCTAHub: React.FC<{ onGetStarted: () => void }> = ({
	onGetStarted,
}) => {
	const [isHovered, setIsHovered] = useState(false);

	const iconData = [
		{ icon: FiCoffee, angle: 0, radius: 140, animationDelay: 0 },
		{ icon: FiCamera, angle: 72, radius: 140, animationDelay: 0.1 },
		{ icon: FiHeart, angle: 144, radius: 140, animationDelay: 0.2 },
		{ icon: FiZap, angle: 216, radius: 140, animationDelay: 0.3 },
		{ icon: FiGlobe, angle: 288, radius: 140, animationDelay: 0.4 },
	];

	return (
		<div className='flex flex-col items-center space-y-6'>
			{/* Main Hub Circle - Responsive */}
			<div
				className='relative cursor-pointer group'
				onMouseEnter={() => setIsHovered(true)}
				onMouseLeave={() => setIsHovered(false)}
				onClick={onGetStarted}>
				{/* Main Hub Circle */}
				<div
					className='relative w-48 h-48 sm:w-56 sm:h-56 lg:w-64 lg:h-64 rounded-full flex flex-col items-center justify-center border-2 backdrop-blur-sm transition-all duration-500 overflow-hidden mx-auto'
					style={{
						background: `linear-gradient(135deg, ${colors.neutral.cloudWhite} 0%, ${colors.ui.blue50} 100%)`,
						borderColor: colors.brand.blue,
						transform: isHovered ? 'scale(1.05)' : 'scale(1)',
						boxShadow: isHovered
							? `0 20px 40px rgba(51, 194, 255, 0.2)`
							: 'none',
					}}>
					{/* Center Icon */}
					<div
						className='w-12 h-12 sm:w-14 sm:h-14 lg:w-16 lg:h-16 rounded-full flex items-center justify-center mb-3 lg:mb-4 transition-all duration-500'
						style={{
							background: `linear-gradient(135deg, ${colors.brand.navy} 0%, ${colors.brand.blue} 100%)`,
							transform: isHovered ? 'scale(1.2) rotate(10deg)' : 'scale(1)',
						}}>
						<FiTrendingUp className='w-6 h-6 sm:w-7 sm:h-7 lg:w-8 lg:h-8 text-white' />
					</div>

					<h2
						className='text-lg sm:text-xl lg:text-xl font-bold text-center mb-2 transition-all duration-300 px-2'
						style={{
							color: colors.brand.navy,
							transform: isHovered ? 'scale(1.1)' : 'scale(1)',
						}}>
						Start Exploring
					</h2>
					<p
						className='text-xs sm:text-sm text-center px-3 lg:px-4'
						style={{ color: colors.neutral.slateGray }}>
						Discover Istanbul through AI conversation
					</p>
				</div>

				{/* Floating icons around the hub - Responsive positioning */}
				<div className='absolute inset-0 pointer-events-none'>
					{iconData.map(
						({ icon: Icon, angle, radius, animationDelay }, index) => {
							// Responsive radius based on screen size
							const responsiveRadius = radius * 0.7; // Reduce radius for better fit
							const x = Math.cos((angle * Math.PI) / 180) * responsiveRadius;
							const y = Math.sin((angle * Math.PI) / 180) * responsiveRadius;

							return (
								<div
									key={index}
									className='absolute transition-all duration-500 hidden sm:block'
									style={{
										left: `calc(50% + ${x}px - 20px)`,
										top: `calc(50% + ${y}px - 20px)`,
										transform: isHovered ? 'scale(1.3)' : 'scale(1)',
										transitionDelay: isHovered ? `${animationDelay}s` : '0s',
									}}>
									{/* Icon Circle */}
									<div
										className='w-8 h-8 lg:w-10 lg:h-10 rounded-full flex items-center justify-center transition-all duration-500'
										style={{
											background: isHovered
												? `linear-gradient(135deg, ${colors.brand.blue} 0%, ${colors.brand.green} 100%)`
												: `linear-gradient(135deg, ${colors.supporting.lightBlue} 0%, ${colors.supporting.mintGreen} 100%)`,
											boxShadow: isHovered
												? `0 8px 25px rgba(51, 194, 255, 0.4)`
												: 'none',
											transitionDelay: isHovered ? `${animationDelay}s` : '0s',
										}}>
										<Icon
											className='w-4 h-4 lg:w-5 lg:h-5 transition-all duration-500'
											style={{
												color: isHovered ? 'white' : colors.brand.navy,
												transform: isHovered
													? `rotate(${15 + index * 5}deg) scale(1.1)`
													: 'rotate(0deg)',
												transitionDelay: isHovered
													? `${animationDelay}s`
													: '0s',
											}}
										/>
									</div>
								</div>
							);
						}
					)}
				</div>
			</div>

			{/* Feature badges below the hub */}
			<div className='flex flex-wrap justify-center gap-2 sm:gap-3 max-w-md'>
				{LANDING_PAGE_DATA.featureBadges.map(({ icon, text }, index) => {
					const iconMap: { [key: string]: React.ComponentType<any> } = {
						FiGlobe,
						FiMessageCircle,
						FiMapPin,
					};
					const Icon = iconMap[icon] || FiMapPin;
					return (
						<div
							key={index}
							className='flex items-center space-x-2 px-3 py-2 rounded-full border text-xs sm:text-sm'
							style={{
								background: `linear-gradient(135deg, ${colors.ui.blue50} 0%, ${colors.ui.green50} 100%)`,
								borderColor: colors.ui.gray200,
							}}>
							<Icon
								className='w-3 h-3 sm:w-4 sm:h-4'
								style={{ color: colors.brand.blue }}
							/>
							<span
								className='font-medium'
								style={{ color: colors.neutral.textBlack }}>
								{text}
							</span>
						</div>
					);
				})}
			</div>
		</div>
	);
};

const CompanyBranding: React.FC = () => (
	<div className='flex flex-col items-center lg:items-start space-y-6 lg:space-y-8 text-center lg:text-left w-full'>
		{/* Logo and Company Name - Side by Side Layout */}
		<div className='flex flex-col lg:flex-row items-center lg:items-center space-y-4 lg:space-y-0 lg:space-x-6 w-full'>
			{/* Logo - Bigger with minimal padding */}
			<div className='flex-shrink-0 p-[5px]'>
				<img
					src='/logo/512x512.png'
					alt='Wizlop Logo'
					className='w-48 h-48 sm:w-56 sm:h-56 md:w-64 md:h-64 lg:w-48 lg:h-48 xl:w-56 xl:h-56 object-contain drop-shadow-2xl hover:scale-105 transition-all duration-500 ease-out'
					style={{
						filter: 'drop-shadow(0 25px 50px rgba(1, 3, 79, 0.15))',
					}}
				/>
			</div>

			{/* Company Name and Tagline - Aligned with Logo */}
			<div className='flex flex-col items-center lg:items-start space-y-2 lg:space-y-3 flex-1'>
				{/* Company Name with Enhanced Styling */}
				<div className='relative'>
					<h1
						className='text-5xl sm:text-6xl md:text-7xl lg:text-5xl xl:text-6xl font-black bg-clip-text text-transparent leading-tight tracking-tight'
						style={{
							backgroundImage: `linear-gradient(135deg, ${colors.brand.navy} 0%, ${colors.brand.blue} 50%, ${colors.brand.green} 100%)`,
							WebkitBackgroundClip: 'text',
							WebkitTextFillColor: 'transparent',
						}}>
						{LANDING_PAGE_DATA.branding.companyName}
					</h1>
					{/* Subtle glow effect behind text */}
					<div
						className='absolute inset-0 text-5xl sm:text-6xl md:text-7xl lg:text-5xl xl:text-6xl font-black leading-tight tracking-tight opacity-20 blur-sm -z-10'
						style={{ color: colors.brand.blue }}>
						{LANDING_PAGE_DATA.branding.companyName}
					</div>
				</div>

				{/* Enhanced Tagline */}
				<div className='relative'>
					<p
						className='text-xl sm:text-2xl md:text-3xl lg:text-xl xl:text-2xl font-semibold leading-relaxed'
						style={{
							color: colors.neutral.slateGray,
							textShadow: `0 2px 4px rgba(0, 0, 0, 0.1)`,
						}}>
						{LANDING_PAGE_DATA.branding.tagline}
					</p>
					{/* Decorative underline */}
					<div
						className='mt-1 h-0.5 w-20 lg:w-24 mx-auto lg:mx-0 rounded-full'
						style={{
							background: `linear-gradient(90deg, ${colors.brand.blue} 0%, ${colors.brand.green} 100%)`,
						}}
					/>
				</div>

				{/* Additional descriptive text */}
				<p
					className='text-base sm:text-lg md:text-xl lg:text-base xl:text-lg font-medium max-w-sm leading-relaxed mt-2'
					style={{ color: colors.neutral.textBlack }}>
					{LANDING_PAGE_DATA.branding.description}
				</p>
			</div>
		</div>

		{/* Main Description - Responsive */}
		<div className='max-w-2xl lg:max-w-lg text-center lg:text-left space-y-4 lg:space-y-6'>
			<h2
				className='text-3xl sm:text-4xl md:text-5xl lg:text-3xl xl:text-4xl font-bold leading-tight'
				style={{ color: colors.brand.navy }}>
				{LANDING_PAGE_DATA.branding.heroTitle}
			</h2>
			<p
				className='text-lg sm:text-xl md:text-2xl lg:text-lg xl:text-xl leading-relaxed'
				style={{ color: colors.neutral.slateGray }}>
				{LANDING_PAGE_DATA.branding.heroDescription}
			</p>
		</div>
	</div>
);

const FloatingHeroIslands: React.FC<FloatingHeroIslandsProps> = ({
	onGetStarted,
}) => {
	return (
		<div className='relative min-h-screen overflow-hidden bg-transparent'>
			{/* Background Features */}
			{React.createElement(require('./ParallaxFeatureSection').default)}

			{/* Particle Background */}
			<div className='absolute inset-0'>
				{/* Animated background particles */}
				{Array.from({ length: 20 }).map((_, i) => (
					<div
						key={i}
						className='absolute w-2 h-2 rounded-full opacity-30 animate-pulse'
						style={{
							left: `${Math.random() * 100}%`,
							top: `${Math.random() * 100}%`,
							background:
								i % 3 === 0
									? colors.brand.blue
									: i % 3 === 1
									? colors.brand.green
									: colors.supporting.lightBlue,
							animationDelay: `${Math.random() * 3}s`,
							animationDuration: `${2 + Math.random() * 2}s`,
						}}></div>
				))}
			</div>

			{/* Main Content Container */}
			<div className='relative min-h-screen py-12 lg:py-20'>
				<div className='max-w-7xl mx-auto px-4 sm:px-6 lg:px-8'>
					{/* Mobile/Small Screen Layout - Centered Single Column */}
					<div className='flex flex-col space-y-12 min-h-[80vh] justify-center lg:hidden'>
						{/* Company Branding - Centered */}
						<div className='flex justify-center'>
							<CompanyBranding />
						</div>

						{/* Floating Cards - Stacked */}
						<div className='space-y-8'>
							<div className='flex justify-center'>
								<div className='w-full max-w-sm'>
									<FloatingIsland
										id='search-preview'
										position={{ x: 0, y: 0, z: 1 }}
										hoverEffect='lift'
										delay={200}>
										<SearchPreviewCard />
									</FloatingIsland>
								</div>
							</div>

							<div className='flex justify-center'>
								<div className='w-full max-w-sm'>
									<FloatingIsland
										id='ai-chat'
										position={{ x: 0, y: 0, z: 1 }}
										hoverEffect='glow'
										delay={400}>
										<AIChatBubble />
									</FloatingIsland>
								</div>
							</div>

							<div className='flex justify-center'>
								<CentralCTAHub onGetStarted={onGetStarted} />
							</div>
						</div>
					</div>

					{/* Large Screen Layout - Original 2-Column */}
					<div className='hidden lg:grid lg:grid-cols-2 gap-16 items-center min-h-[80vh]'>
						{/* Left Side - Company Branding */}
						<div className='flex items-center justify-start'>
							<CompanyBranding />
						</div>

						{/* Right Side - Floating Cards Layout */}
						<div className='relative'>
							{/* Responsive Grid Container for Islands */}
							<div className='grid grid-cols-1 xl:grid-cols-2 gap-8 mb-8'>
								{/* Search Preview Island */}
								<div className='flex justify-center xl:justify-end'>
									<div className='w-full max-w-sm'>
										<FloatingIsland
											id='search-preview'
											position={{ x: 0, y: 0, z: 1 }}
											hoverEffect='lift'
											delay={200}>
											<SearchPreviewCard />
										</FloatingIsland>
									</div>
								</div>

								{/* AI Chat Island */}
								<div className='flex justify-center xl:justify-start'>
									<div className='w-full max-w-sm'>
										<FloatingIsland
											id='ai-chat'
											position={{ x: 0, y: 0, z: 1 }}
											hoverEffect='glow'
											delay={400}>
											<AIChatBubble />
										</FloatingIsland>
									</div>
								</div>
							</div>

							{/* Central CTA Hub */}
							<div className='flex justify-center'>
								<CentralCTAHub onGetStarted={onGetStarted} />
							</div>
						</div>
					</div>
				</div>
			</div>

			{/* Scroll Indicator */}
			<div className='absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce'>
				<div
					className='w-6 h-10 border-2 rounded-full flex justify-center'
					style={{ borderColor: colors.brand.blue }}>
					<div
						className='w-1 h-3 rounded-full mt-2 animate-pulse'
						style={{ background: colors.brand.blue }}></div>
				</div>
			</div>
		</div>
	);
};

export default FloatingHeroIslands;
